// Utility functions for checking color contrast ratios

/**
 * Convert HSL to RGB
 */
function hslToRgb(h: number, s: number, l: number): [number, number, number] {
  h /= 360;
  s /= 100;
  l /= 100;

  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h * 6) % 2 - 1));
  const m = l - c / 2;

  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 1/6) {
    r = c; g = x; b = 0;
  } else if (1/6 <= h && h < 2/6) {
    r = x; g = c; b = 0;
  } else if (2/6 <= h && h < 3/6) {
    r = 0; g = c; b = x;
  } else if (3/6 <= h && h < 4/6) {
    r = 0; g = x; b = c;
  } else if (4/6 <= h && h < 5/6) {
    r = x; g = 0; b = c;
  } else if (5/6 <= h && h < 1) {
    r = c; g = 0; b = x;
  }

  return [
    Math.round((r + m) * 255),
    Math.round((g + m) * 255),
    Math.round((b + m) * 255)
  ];
}

/**
 * Calculate relative luminance of a color
 */
function getLuminance(r: number, g: number, b: number): number {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

/**
 * Calculate contrast ratio between two colors
 */
export function getContrastRatio(color1: [number, number, number], color2: [number, number, number]): number {
  const lum1 = getLuminance(...color1);
  const lum2 = getLuminance(...color2);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
}

/**
 * Parse HSL string and convert to RGB
 */
export function parseHslToRgb(hslString: string): [number, number, number] {
  // Parse "221 25% 55%" format
  const matches = hslString.match(/(\d+)\s+(\d+)%\s+(\d+)%/);
  if (!matches) {
    throw new Error('Invalid HSL format');
  }
  
  const h = parseInt(matches[1]);
  const s = parseInt(matches[2]);
  const l = parseInt(matches[3]);
  
  return hslToRgb(h, s, l);
}

/**
 * Check if contrast ratio meets WCAG standards
 */
export function checkWCAGCompliance(contrastRatio: number, level: 'AA' | 'AAA' = 'AA', isLargeText: boolean = false): {
  passes: boolean;
  level: string;
  ratio: number;
  required: number;
} {
  let required: number;
  
  if (level === 'AAA') {
    required = isLargeText ? 4.5 : 7;
  } else {
    required = isLargeText ? 3 : 4.5;
  }
  
  return {
    passes: contrastRatio >= required,
    level,
    ratio: contrastRatio,
    required
  };
}

/**
 * Test current theme colors for WCAG compliance
 */
export function testThemeContrast(): {
  foregroundOnBackground: any;
  mutedOnCard: any;
  cardForegroundOnCard: any;
  primaryOnPrimaryForeground: any;
} {
  // Light theme colors
  const background = parseHslToRgb('218 93% 95%');
  const foreground = parseHslToRgb('221 25% 15%');
  const card = parseHslToRgb('0 0% 100%');
  const cardForeground = parseHslToRgb('221 25% 15%');
  const mutedForeground = parseHslToRgb('221 25% 35%');
  const primary = parseHslToRgb('221 44% 41%');
  const primaryForeground = parseHslToRgb('0 0% 100%');

  return {
    foregroundOnBackground: checkWCAGCompliance(getContrastRatio(foreground, background)),
    mutedOnCard: checkWCAGCompliance(getContrastRatio(mutedForeground, card)),
    cardForegroundOnCard: checkWCAGCompliance(getContrastRatio(cardForeground, card)),
    primaryOnPrimaryForeground: checkWCAGCompliance(getContrastRatio(primary, primaryForeground))
  };
}

/**
 * Generate accessible color suggestions
 */
export function suggestAccessibleColors(baseHue: number, saturation: number): {
  lightTheme: {
    background: string;
    foreground: string;
    mutedForeground: string;
    card: string;
    cardForeground: string;
  };
  darkTheme: {
    background: string;
    foreground: string;
    mutedForeground: string;
    card: string;
    cardForeground: string;
  };
} {
  return {
    lightTheme: {
      background: `${baseHue} ${Math.min(saturation, 20)}% 98%`,
      foreground: `${baseHue} ${Math.min(saturation, 30)}% 10%`,
      mutedForeground: `${baseHue} ${Math.min(saturation, 25)}% 30%`,
      card: '0 0% 100%',
      cardForeground: `${baseHue} ${Math.min(saturation, 30)}% 10%`
    },
    darkTheme: {
      background: `${baseHue} ${Math.min(saturation, 50)}% 8%`,
      foreground: `${baseHue} ${Math.min(saturation, 30)}% 98%`,
      mutedForeground: `${baseHue} ${Math.min(saturation, 30)}% 85%`,
      card: `${baseHue} ${Math.min(saturation, 50)}% 12%`,
      cardForeground: `${baseHue} ${Math.min(saturation, 30)}% 98%`
    }
  };
}

/**
 * Real-time contrast checker for development
 */
export function createContrastChecker() {
  if (typeof window === 'undefined') return;

  const checker = {
    checkElement: (element: HTMLElement) => {
      const styles = window.getComputedStyle(element);
      const color = styles.color;
      const backgroundColor = styles.backgroundColor;
      
      // This is a simplified version - in practice you'd need to parse CSS colors
      console.log('Element contrast check:', {
        element: element.tagName,
        color,
        backgroundColor,
        // Add actual contrast calculation here
      });
    },
    
    scanPage: () => {
      const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, a, button');
      const issues: Array<{element: HTMLElement; issue: string}> = [];
      
      textElements.forEach(el => {
        const htmlEl = el as HTMLElement;
        if (htmlEl.classList.contains('text-muted-foreground')) {
          // Check if this element might have contrast issues
          const rect = htmlEl.getBoundingClientRect();
          if (rect.width > 0 && rect.height > 0) {
            // Element is visible, check contrast
            checker.checkElement(htmlEl);
          }
        }
      });
      
      return issues;
    }
  };
  
  return checker;
}

// Export for use in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).contrastChecker = createContrastChecker();
  (window as any).testThemeContrast = testThemeContrast;
}
