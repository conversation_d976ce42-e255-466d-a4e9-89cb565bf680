'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

interface AccessibilityContextType {
  highContrast: boolean;
  reducedMotion: boolean;
  fontSize: 'normal' | 'large' | 'extra-large';
  toggleHighContrast: () => void;
  setFontSize: (size: 'normal' | 'large' | 'extra-large') => void;
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

export function useAccessibility() {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
}

interface AccessibilityProviderProps {
  children: React.ReactNode;
}

export function AccessibilityProvider({ children }: AccessibilityProviderProps) {
  const [highContrast, setHighContrast] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [fontSize, setFontSizeState] = useState<'normal' | 'large' | 'extra-large'>('normal');

  useEffect(() => {
    // Check for user preferences
    const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    setHighContrast(prefersHighContrast);
    setReducedMotion(prefersReducedMotion);

    // Load saved preferences
    const savedHighContrast = localStorage.getItem('accessibility-high-contrast');
    const savedFontSize = localStorage.getItem('accessibility-font-size');
    
    if (savedHighContrast) {
      setHighContrast(JSON.parse(savedHighContrast));
    }
    
    if (savedFontSize) {
      setFontSizeState(savedFontSize as 'normal' | 'large' | 'extra-large');
    }
  }, []);

  useEffect(() => {
    // Apply accessibility classes to document
    const root = document.documentElement;
    
    if (highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    if (reducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }
    
    // Apply font size
    root.classList.remove('font-large', 'font-extra-large');
    if (fontSize === 'large') {
      root.classList.add('font-large');
    } else if (fontSize === 'extra-large') {
      root.classList.add('font-extra-large');
    }
    
    // Apply accessible text classes to common elements
    const applyAccessibleClasses = () => {
      // Apply to muted text elements
      const mutedElements = document.querySelectorAll('.text-muted-foreground');
      mutedElements.forEach(el => {
        el.classList.add('accessible-text-muted');
      });
      
      // Apply to card elements
      const cardElements = document.querySelectorAll('.bg-card');
      cardElements.forEach(el => {
        el.classList.add('accessible-card');
      });
      
      // Apply to links
      const linkElements = document.querySelectorAll('a');
      linkElements.forEach(el => {
        if (!el.classList.contains('accessible-link')) {
          el.classList.add('accessible-link', 'accessible-focus');
        }
      });
      
      // Apply to buttons
      const buttonElements = document.querySelectorAll('button');
      buttonElements.forEach(el => {
        if (!el.classList.contains('accessible-button')) {
          el.classList.add('accessible-button', 'accessible-focus');
        }
      });
    };
    
    // Apply classes immediately and after DOM changes
    applyAccessibleClasses();
    
    // Use MutationObserver to apply classes to dynamically added elements
    const observer = new MutationObserver(() => {
      applyAccessibleClasses();
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    return () => {
      observer.disconnect();
    };
  }, [highContrast, reducedMotion, fontSize]);

  const toggleHighContrast = () => {
    const newValue = !highContrast;
    setHighContrast(newValue);
    localStorage.setItem('accessibility-high-contrast', JSON.stringify(newValue));
  };

  const setFontSize = (size: 'normal' | 'large' | 'extra-large') => {
    setFontSizeState(size);
    localStorage.setItem('accessibility-font-size', size);
  };

  return (
    <AccessibilityContext.Provider
      value={{
        highContrast,
        reducedMotion,
        fontSize,
        toggleHighContrast,
        setFontSize,
      }}
    >
      {children}
    </AccessibilityContext.Provider>
  );
}

// Accessibility toolbar component
export function AccessibilityToolbar() {
  const { highContrast, fontSize, toggleHighContrast, setFontSize } = useAccessibility();

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-card border rounded-lg p-3 shadow-lg accessible-card">
      <div className="flex flex-col gap-2 text-sm">
        <h3 className="font-semibold accessible-text">إعدادات إمكانية الوصول</h3>
        
        <button
          onClick={toggleHighContrast}
          className="text-right px-2 py-1 rounded accessible-button"
          aria-pressed={highContrast}
        >
          {highContrast ? '✓' : '○'} تباين عالي
        </button>
        
        <div className="flex flex-col gap-1">
          <span className="accessible-text-muted">حجم الخط:</span>
          <div className="flex gap-1">
            {(['normal', 'large', 'extra-large'] as const).map((size) => (
              <button
                key={size}
                onClick={() => setFontSize(size)}
                className={`px-2 py-1 text-xs rounded accessible-button ${
                  fontSize === size ? 'bg-primary text-primary-foreground' : ''
                }`}
                aria-pressed={fontSize === size}
              >
                {size === 'normal' ? 'عادي' : size === 'large' ? 'كبير' : 'كبير جداً'}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Hook to check if accessibility toolbar should be shown
export function useShowAccessibilityToolbar() {
  const [show, setShow] = useState(false);

  useEffect(() => {
    // Show toolbar if user has accessibility preferences or on keyboard navigation
    const checkAccessibilityNeeds = () => {
      const hasHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
      const hasReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      const hasLargeText = window.matchMedia('(prefers-reduced-data: reduce)').matches;
      
      if (hasHighContrast || hasReducedMotion || hasLargeText) {
        setShow(true);
      }
    };

    checkAccessibilityNeeds();

    // Show on keyboard navigation
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        setShow(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return show;
}
