// Accessibility Configuration and Testing Setup

module.exports = {
  // WCAG compliance level
  wcagLevel: 'AA',
  
  // Color contrast requirements
  colorContrast: {
    normal: 4.5,
    large: 3.0,
    enhanced: 7.0,
    largeEnhanced: 4.5
  },
  
  // Supported browsers for testing
  browsers: [
    'Chrome >= 90',
    'Firefox >= 88',
    'Safari >= 14',
    'Edge >= 90'
  ],
  
  // Screen readers to test with
  screenReaders: [
    'NVDA',
    'JAWS',
    'VoiceOver',
    'TalkBack'
  ],
  
  // Accessibility testing rules
  rules: {
    // Color and contrast
    'color-contrast': 'error',
    'color-contrast-enhanced': 'warn',
    
    // Keyboard navigation
    'keyboard': 'error',
    'focus-order-semantics': 'error',
    'tabindex': 'error',
    
    // Screen reader support
    'label': 'error',
    'image-alt': 'error',
    'heading-order': 'error',
    'landmark-unique': 'error',
    
    // ARIA
    'aria-valid-attr': 'error',
    'aria-valid-attr-value': 'error',
    'aria-required-attr': 'error',
    
    // Language
    'html-has-lang': 'error',
    'html-lang-valid': 'error',
    
    // Structure
    'page-has-heading-one': 'error',
    'bypass': 'error'
  },
  
  // Ignore certain elements/selectors
  ignore: [
    '.adsense-container',
    '[data-testid="analytics"]'
  ],
  
  // Custom accessibility checks
  customChecks: {
    // Check Arabic text direction
    'arabic-text-direction': {
      selector: '[lang="ar"], [dir="rtl"]',
      check: (element) => {
        const computedStyle = window.getComputedStyle(element);
        return computedStyle.direction === 'rtl';
      },
      message: 'Arabic text should have RTL direction'
    },
    
    // Check contrast for Arabic fonts
    'arabic-font-contrast': {
      selector: '.arabic-text',
      check: (element) => {
        // Custom logic for Arabic font contrast
        return true; // Placeholder
      },
      message: 'Arabic text needs sufficient contrast'
    },
    
    // Check skip link functionality
    'skip-link-functional': {
      selector: '.skip-to-content',
      check: (element) => {
        const href = element.getAttribute('href');
        const target = document.querySelector(href);
        return target && target.getAttribute('tabindex') === '-1';
      },
      message: 'Skip link target should be focusable'
    }
  },
  
  // Testing environments
  environments: {
    development: {
      showReport: true,
      logViolations: true,
      breakOnViolations: false
    },
    testing: {
      showReport: false,
      logViolations: true,
      breakOnViolations: true
    },
    production: {
      showReport: false,
      logViolations: false,
      breakOnViolations: false
    }
  },
  
  // Performance thresholds
  performance: {
    maxCSSSize: '50kb',
    maxJSSize: '100kb',
    maxLoadTime: '2s'
  },
  
  // Reporting configuration
  reporting: {
    format: ['json', 'html'],
    outputDir: './accessibility-reports',
    includeScreenshots: true,
    includeCodeSnippets: true
  }
};

// Axe-core configuration for automated testing
const axeConfig = {
  rules: {
    'color-contrast': { enabled: true },
    'keyboard': { enabled: true },
    'focus-order-semantics': { enabled: true },
    'image-alt': { enabled: true },
    'label': { enabled: true },
    'heading-order': { enabled: true },
    'landmark-unique': { enabled: true },
    'page-has-heading-one': { enabled: true },
    'bypass': { enabled: true },
    'html-has-lang': { enabled: true },
    'html-lang-valid': { enabled: true }
  },
  tags: ['wcag2a', 'wcag2aa', 'wcag21aa'],
  exclude: [
    '.adsense-container',
    '[data-testid="analytics"]'
  ]
};

// Lighthouse accessibility configuration
const lighthouseConfig = {
  extends: 'lighthouse:default',
  settings: {
    onlyCategories: ['accessibility'],
    skipAudits: [
      'uses-http2',
      'uses-optimized-images',
      'uses-webp-images'
    ]
  },
  audits: [
    'accessibility/accesskeys',
    'accessibility/aria-allowed-attr',
    'accessibility/aria-required-attr',
    'accessibility/aria-required-children',
    'accessibility/aria-required-parent',
    'accessibility/aria-roles',
    'accessibility/aria-valid-attr',
    'accessibility/aria-valid-attr-value',
    'accessibility/button-name',
    'accessibility/bypass',
    'accessibility/color-contrast',
    'accessibility/document-title',
    'accessibility/duplicate-id',
    'accessibility/form-field-multiple-labels',
    'accessibility/frame-title',
    'accessibility/heading-order',
    'accessibility/html-has-lang',
    'accessibility/html-lang-valid',
    'accessibility/image-alt',
    'accessibility/input-image-alt',
    'accessibility/label',
    'accessibility/link-name',
    'accessibility/list',
    'accessibility/listitem',
    'accessibility/meta-refresh',
    'accessibility/meta-viewport',
    'accessibility/object-alt',
    'accessibility/tabindex',
    'accessibility/td-headers-attr',
    'accessibility/th-has-data-cells',
    'accessibility/valid-lang'
  ]
};

// Pa11y configuration for command-line testing
const pa11yConfig = {
  standard: 'WCAG2AA',
  ignore: [
    'WCAG2AA.Principle1.Guideline1_4.1_4_3.G18.Fail',
    'WCAG2AA.Principle4.Guideline4_1.4_1_2.H91.A.EmptyNoId'
  ],
  includeNotices: false,
  includeWarnings: true,
  chromeLaunchConfig: {
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  },
  actions: [
    'wait for element .main-content to be visible',
    'set field #search to "test"',
    'click element .search-button',
    'wait for path to not be /search'
  ]
};

module.exports.axeConfig = axeConfig;
module.exports.lighthouseConfig = lighthouseConfig;
module.exports.pa11yConfig = pa11yConfig;
