'use client';

import React from 'react';

interface SkipToContentProps {
  targetId?: string;
  children?: React.ReactNode;
}

export function SkipToContent({ targetId = 'main-content', children }: SkipToContentProps) {
  const handleSkip = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    const target = document.getElementById(targetId);
    if (target) {
      target.focus();
      target.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <a
      href={`#${targetId}`}
      className="skip-to-content"
      onClick={handleSkip}
      onKeyDown={(e) => {
        if (e.key === 'Enter') {
          handleSkip(e as any);
        }
      }}
    >
      {children || 'تخطي إلى المحتوى الرئيسي'}
    </a>
  );
}

// Component to mark main content area
export function MainContent({ children, className = '', ...props }: React.HTMLAttributes<HTMLElement>) {
  return (
    <main
      id="main-content"
      tabIndex={-1}
      className={`focus:outline-none ${className}`}
      {...props}
    >
      {children}
    </main>
  );
}
