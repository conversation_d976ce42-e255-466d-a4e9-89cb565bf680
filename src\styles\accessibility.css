/* Accessibility Enhancements for Better Color Contrast */

/* WCAG AA Compliant Color Overrides */
:root {
  /* Enhanced contrast ratios for better accessibility */
  --accessible-text-primary: 221 25% 10%;
  --accessible-text-secondary: 221 25% 25%;
  --accessible-text-muted: 221 25% 35%;
  --accessible-bg-card: 0 0% 100%;
  --accessible-border: 221 20% 80%;
}

.dark {
  --accessible-text-primary: 220 30% 98%;
  --accessible-text-secondary: 220 30% 90%;
  --accessible-text-muted: 220 30% 85%;
  --accessible-bg-card: 221 44% 15%;
  --accessible-border: 221 44% 30%;
}

/* High contrast mode overrides */
@media (prefers-contrast: high) {
  :root {
    --accessible-text-primary: 0 0% 0%;
    --accessible-text-secondary: 0 0% 10%;
    --accessible-text-muted: 0 0% 20%;
    --accessible-bg-card: 0 0% 100%;
    --accessible-border: 0 0% 50%;
  }

  .dark {
    --accessible-text-primary: 0 0% 100%;
    --accessible-text-secondary: 0 0% 95%;
    --accessible-text-muted: 0 0% 90%;
    --accessible-bg-card: 0 0% 0%;
    --accessible-border: 0 0% 50%;
  }
}

/* Apply accessible colors to specific elements */
.accessible-text {
  color: hsl(var(--accessible-text-primary)) !important;
}

.accessible-text-secondary {
  color: hsl(var(--accessible-text-secondary)) !important;
}

.accessible-text-muted {
  color: hsl(var(--accessible-text-muted)) !important;
  font-weight: 450;
}

/* Card components with better contrast */
.accessible-card {
  background-color: hsl(var(--accessible-bg-card));
  border-color: hsl(var(--accessible-border));
  color: hsl(var(--accessible-text-primary));
}

.accessible-card .text-muted-foreground {
  color: hsl(var(--accessible-text-muted)) !important;
}

/* Focus indicators for better accessibility */
.accessible-focus:focus,
.accessible-focus:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--primary));
}

/* Enhanced link contrast */
.accessible-link {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

.accessible-link:hover,
.accessible-link:focus {
  color: hsl(var(--primary));
  filter: brightness(1.2);
  text-decoration-thickness: 2px;
}

/* Better contrast for small text */
.text-xs.accessible-text,
.text-sm.accessible-text {
  font-weight: 500;
  color: hsl(var(--accessible-text-secondary)) !important;
}

/* Ensure sufficient contrast for Arabic text */
.arabic-text.accessible-text {
  font-weight: 450;
  letter-spacing: 0.01em;
}

.dark .arabic-text.accessible-text {
  font-weight: 400;
  letter-spacing: 0.025em;
}

/* Button accessibility improvements */
.accessible-button {
  font-weight: 500;
  border: 1px solid transparent;
}

.accessible-button:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Form input accessibility */
.accessible-input {
  border: 2px solid hsl(var(--accessible-border));
  color: hsl(var(--accessible-text-primary));
}

.accessible-input:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
}

/* Utility classes for quick accessibility fixes */
.contrast-high {
  color: hsl(var(--accessible-text-primary)) !important;
  font-weight: 500;
}

.contrast-medium {
  color: hsl(var(--accessible-text-secondary)) !important;
  font-weight: 450;
}

.contrast-low {
  color: hsl(var(--accessible-text-muted)) !important;
  font-weight: 400;
}

/* Responsive accessibility adjustments */
@media (max-width: 640px) {
  .accessible-text-muted {
    font-weight: 500;
    color: hsl(var(--accessible-text-secondary)) !important;
  }
  
  .text-sm.accessible-text {
    font-size: 0.95rem;
    font-weight: 500;
  }
}

/* Print accessibility */
@media print {
  .accessible-text,
  .accessible-text-secondary,
  .accessible-text-muted {
    color: black !important;
    font-weight: 500;
  }
  
  .accessible-card {
    background: white !important;
    border: 1px solid black !important;
  }
}

/* Motion preferences */
@media (prefers-reduced-motion: reduce) {
  .accessible-link,
  .accessible-button,
  .accessible-focus {
    transition: none !important;
  }
}

/* Font size accessibility options */
.font-large {
  font-size: 110% !important;
}

.font-large .text-sm {
  font-size: 0.95rem !important;
}

.font-large .text-xs {
  font-size: 0.85rem !important;
}

.font-large .text-lg {
  font-size: 1.25rem !important;
}

.font-large .text-xl {
  font-size: 1.4rem !important;
}

.font-extra-large {
  font-size: 125% !important;
}

.font-extra-large .text-sm {
  font-size: 1rem !important;
}

.font-extra-large .text-xs {
  font-size: 0.9rem !important;
}

.font-extra-large .text-lg {
  font-size: 1.4rem !important;
}

.font-extra-large .text-xl {
  font-size: 1.6rem !important;
}

/* High contrast mode styles */
.high-contrast {
  --accessible-text-primary: 0 0% 0%;
  --accessible-text-secondary: 0 0% 10%;
  --accessible-text-muted: 0 0% 20%;
  --accessible-bg-card: 0 0% 100%;
  --accessible-border: 0 0% 50%;
}

.high-contrast.dark {
  --accessible-text-primary: 0 0% 100%;
  --accessible-text-secondary: 0 0% 95%;
  --accessible-text-muted: 0 0% 90%;
  --accessible-bg-card: 0 0% 0%;
  --accessible-border: 0 0% 50%;
}

.high-contrast .text-muted-foreground {
  color: hsl(var(--accessible-text-muted)) !important;
  font-weight: 600 !important;
}

.high-contrast .bg-card {
  background-color: hsl(var(--accessible-bg-card)) !important;
  border-color: hsl(var(--accessible-border)) !important;
}

/* Reduced motion styles */
.reduced-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Skip to content link */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  font-weight: 600;
}

.skip-to-content:focus {
  top: 6px;
}
